using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Extension;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;
using Apq.Utils;

using Cysharp.Threading.Tasks;

using DataStructure;

using HotScripts;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     玩家阵营的子弹
    /// </summary>
    public class ActorBulletBase : BulletBase
    {
        /// <inheritdoc />
        public override IList<HitThingCells> DoCollideEnemies(LineSegment line)
        {
            // 子弹可攻击到的胶囊区域
            CapsuleArea capsuleArea = new()
            {
                CircleCenter = BulletThing.Position,
                Radius = (float)GunThing.GetTotalDouble(PropType.BulletRadius)
                    .FirstOrDefault(),
                Dir = line.Dir
            };

            List<HitThingCells> rtn = SingletonMgr.Instance.BattleMgr.Monsters
                .Where(x =>
                    // 怪物已出生
                    x.Area2D.Count > 0 &&
                    // 子弹碰到怪物
                    capsuleArea.IsIntersect(x.CircularArea2D) &&
                    // 排除该子弹不攻击的怪物
                    (!BulletThing.SeparateFrom.HasValue ||
                     BulletThing.SeparateFrom.All(m => !Util.IsEquals(m, x))))
                .OfType<ThingBase>().Select(x => new HitThingCells { Thing = x })
                .ToList();

            return rtn;
        }

        /// <inheritdoc />
        protected override void OnHitEnemy(LineSegment line, ThingBase thing, IList<HitThingCells> enemies)
        {
            enemies.ToList().ForEach(enemy =>
            {
                EffectMgr.Instance.ShowEffect(EffectPath.behit_NvFuZhu01,
                    enemy.Thing.Position, 2).Forget();
            });

            // 伤害
            //var maxRadius = DamageEnemy(gun, enemies);
            DamageEnemy(thing, enemies);
            //Debug.Log($"击中的敌人 最大半径:{maxRadius}");

            // 分裂
            if (BulletThing.SeparateTimes.Value > 0)
            {
                //Debug.Log($"分裂来源子弹:剩余分裂次数{BulletThing.SeparateTimes.Value}");

                // 即将第几次分裂(从0起)
                long separateIdx =
                    BulletThing.CdExecutor.Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault() -
                    BulletThing.SeparateTimes.Value;
                // 分裂概率
                double separatePriority = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.SeparatePriorityList)
                    .IndexOfOrLastOrDefault((int)separateIdx);

                // if (RandomNum.RandomDouble(0, 1) < separatePriority)
                {
                    // 剩余分裂次数-1
                    BulletThing.SeparateTimes.Value--;

                    // 击中的怪物
                    List<MonsterThing> monsters = enemies.Select(x => x.Thing).OfType<MonsterThing>().ToList();

                    // 分裂的基准方向,默认取子弹的移动方向
                    Vector3 separateDir_1 = BulletThing.MoveDirection_Straight.Value;
                    if (BulletThing.CdExecutor.Thing.GetTotalLong(PropType.SeparateDir).FirstOrDefault() == 1)
                    {
                        // 最近敌人方向
                        DistanceThing distanceMonster = SingletonMgr.Instance.BattleMgr.FindMonster(line.PosStart,
                                (float)BulletThing.GetTotalDouble(PropType.Radius).FirstOrDefault(),
                                FindActionTarget.NearestEnemy,
                                (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault(),
                                1, 0,
                                // 分裂出来的子弹不打刚被击中的怪物
                                m => monsters.All(e => e.Guid != m.Guid))
                            .FirstOrDefault();
                        if (distanceMonster is { Thing2: not null })
                        {
                            separateDir_1 = (distanceMonster.Thing2.Position - line.PosStart).normalized;
                        }
                    }

                    // 分裂角度、分裂数量
                    List<float> angles = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.SeparateAnglesList)
                        .ConvertAll(x => (float)x);
                    long separateCount = BulletThing.CdExecutor.Thing.GetTotalLong(PropType.SeparateCount)
                        .IndexOf_ByCycle(BulletThing.SeparateNo.Value);

                    // 按分裂概率调整分裂数量
                    if (separatePriority <= 0 || RandomNum.RandomDouble(0, 1) >= separatePriority)
                    {
                        separateCount = Math.Clamp(separateCount, 0, 1);
                    }

                    for (int i = 0; i < separateCount; i++)
                    {
                        // 从分裂的基准方向,按分裂角度创建新子弹
                        float angle = angles.IndexOf_ByCycle(i);
                        Vector3 dirSeparate_1 = separateDir_1.RotateAround(Vector3.forward, angle);

                        // 创建分裂产生的子弹
                        BulletThing bulletSeparate = BulletThing.CdExecutor.Thing.CreateBullet(BulletThing.CdExecutor,
                            null, null, 0,
                            BulletThing.PenetrateTimes.Value,
                            BulletThing.BounceTimes.Value,
                            BulletThing.SeparateTimes.Value, monsters
                        );
                        //Debug.Log($"创建分裂出来的子弹:剩余分裂次数{bulletSeparate.SeparateTimes.Value}");

                        // 新子弹的位置
                        bulletSeparate.Position = line.PosStart; // + separateDistance * dirSeparate;
                        bulletSeparate.MoveDirection_Straight.Value = dirSeparate_1;

                        MessageBroker.Default.Publish(new BornBullet
                        {
                            Bullet = bulletSeparate,
                            // 新子弹的上一个位置按反方向计算一个值
                            PositionPre = line.PosStart - dirSeparate_1
                        });
                    }
                }
            }

            // 穿透
            if (BulletThing.PenetrateTimes.Value > 0)
            {
                //Debug.Log($"子弹剩余穿透次数(前):{BulletThing.SeparateTimes.Value}");
                // 剩余穿透次数:减去打中的敌人数
                BulletThing.PenetrateTimes.Value -= enemies.Count;
                //Debug.Log($"子弹剩余穿透次数(后):{BulletThing.SeparateTimes.Value}");

                // 仍不小于0,则不应丢弃
                if (BulletThing.PenetrateTimes.Value >= 0)
                {
                    BulletThing.ShouldDiscard.Value = false;
                }
            }
        }

        /// <inheritdoc />
        public override float DamageEnemy(ThingBase thing, IList<HitThingCells> enemies,
            Action<MonsterThing> onDied = null)
        {
            // 受击生物的最大半径
            float maxRadius = 0;
            // 计算伤害量
            foreach (MonsterThing enemy in enemies.Select(x => x.Thing).Cast<MonsterThing>())
            {
                if (maxRadius < enemy.GetTotalDouble(PropType.Radius).FirstOrDefault())
                {
                    maxRadius = (float)enemy.GetTotalDouble(PropType.Radius).FirstOrDefault();
                }

                // 受击方先接受枪携带的Buff
                enemy.ReceiveBuffByBulletHit(thing, BuffRecvType.Hit);

                // 子弹的已分裂次数
                int separateTimes = (int)BulletThing.CdExecutor.Thing.GetTotalLong(PropType.MaxSeparateTimes)
                                        .FirstOrDefault() -
                                    BulletThing.SeparateTimes.Value;

                (double damage, bool isCriticalHit) = Helper.CalcDamage(BulletThing, 0, 0, enemy, separateTimes);

                // 敌人接受伤害,玩家吸血也在其中
                Vector3 pos = enemy.Position;
                bool isDied = enemy.TakeHit(thing, damage, isCriticalHit);

                // 击中的爆炸
                if (BulletThing.GetTotalLong(PropType.BulletType).FirstOrDefault() ==
                    (int)BulletType.Bullet
                    && BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault() >
                    float.Epsilon)
                {
                    DoExplose(SingletonMgr.Instance.BattleMgr.GetCancellationTokenOnDestroy(),
                        thing, pos).Forget();
                }

                if (!isDied)
                {
                    continue;
                }

                onDied?.Invoke(enemy);
                // 击杀的爆炸
                if (BulletThing.GetTotalLong(PropType.BulletType).FirstOrDefault() ==
                    (int)BulletType.Bullet
                    && BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.KilledExploseRadius).FirstOrDefault() >
                    float.Epsilon)
                {
                    DoExplose(SingletonMgr.Instance.BattleMgr.GetCancellationTokenOnDestroy(),
                        thing, pos, 2).Forget();
                }
            }

            return maxRadius;
        }

        /// <inheritdoc />
        protected override async UniTaskVoid DoExplose(CancellationToken token, ThingBase thing,
            Vector3 pos, byte exploseType = 1, int exploseNo = 0)
        {
            try
            {
                // 延时后爆炸:秒-爆炸延时
                float delay = 0f;
                if (exploseType == 2)
                {
                    // 连爆次数用完就不爆炸了
                    if (exploseNo > thing.GetTotalLong(PropType.KilledExploseTimes).FirstOrDefault())
                    {
                        return;
                    }

                    delay = 0.3f;
                    await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }
                }

                // 爆炸概率
                double explosePriority = exploseType switch
                {
                    1 => thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault(),
                    2 => thing.GetTotalDouble(PropType.KilledExplosePriority).FirstOrDefault(),
                    _ => 0
                };

                //Debug.Log((exploseType == 1 ? "击中" : "击杀") + $"爆炸概率:{explosePriority}");

                // 按概率爆炸
                if (exploseNo > 0 // 总是连爆
                    // 首爆按概率
                    || RandomNum.RandomDouble(0, 1) < explosePriority)
                {
                    // 爆炸声音
                    string exploseSound = exploseType == 1
                        ? thing.GetTotalString(PropType.ExploseSound).FirstOrDefault()
                        : thing.GetTotalString(PropType.KilledExploseSound).FirstOrDefault();
                    AudioPlayer.Instance.PlaySound(exploseSound).Forget();
                    // 爆炸特效
                    string exploseEffect = exploseType == 1
                        ? thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault()
                        : thing.GetTotalString(PropType.KilledExploseEffect).FirstOrDefault();
                    // 爆炸半径
                    float exploseRadius = (float)(exploseType == 1
                        ? thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault()
                        : thing.GetTotalDouble(PropType.KilledExploseRadius).FirstOrDefault());

                    Debug.Log($"V60.0 {(exploseType == 1 ? "击中" : "击杀")}爆炸 位置:{pos} 半径:{exploseRadius}");

                    // V60.0 检查是否可以触发爆炸（防止连锁爆炸卡顿）
                    if (!SingletonMgr.Instance.BattleMgr.CanTriggerExplosion())
                    {
                        Debug.Log($"V60.0 爆炸数量已达上限，跳过此次爆炸");
                        return;
                    }

                    // V60.0 增加爆炸计数
                    SingletonMgr.Instance.BattleMgr.IncrementExplosionCount();

                    if (!string.IsNullOrWhiteSpace(exploseEffect))
                    {
                        EffectMgr.Instance.ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect), pos,
                            exploseRadius).Forget();
                    }

                    // V59.0 优化爆炸伤害计算，分层筛选提升性能
                    List<CreatureThing> enemies = GetOptimizedExplosionTargets(pos, exploseRadius);

                    // 爆炸伤害系数
                    float exploseCoe = (float)(exploseType == 1
                        ? thing.GetTotalDouble(PropType.ExploseCoe).FirstOrDefault()
                        : thing.GetTotalDouble(PropType.KilledExploseCoe).FirstOrDefault());
                    foreach (MonsterThing enemy in enemies.Cast<MonsterThing>().Where(x => x != null))
                    {
                        (double damage, bool isCritical) =
                            Helper.CalcDamage(BulletThing, 0, exploseType, enemy, exploseNo);
                        //var normal伤害量 = 伤害量;
                        //伤害量 *= exploseCoe;
                        //if (伤害量 < 1) 伤害量 = 1;

                        //Debug.Log((exploseType == 1 ? "击中" : "击杀") + $"爆炸造成的伤害量:{damage} 爆炸伤害系数:{exploseCoe}");

                        // 受击方先接受枪携带的Buff
                        enemy.ReceiveBuffByBulletHit(thing,
                            exploseType == 2 ? BuffRecvType.KilledExplose : BuffRecvType.Explose);

                        // 敌人接受伤害,玩家吸血也在其中
                        bool IsDied = enemy.TakeHit(thing, damage, isCritical);

                        if (IsDied)
                        {
                            // 被爆炸击杀后,如果还有爆炸次数,则继续连爆
                            DoExplose(token, thing, enemy.Position, 2, exploseNo + 1).Forget();
                        }
                    }
                    
                    // V60.0 爆炸处理完成，减少计数
                    SingletonMgr.Instance.BattleMgr.DecrementExplosionCount();
                }
            }
            catch (OperationCanceledException)
            {
                // V60.0 取消时也要减少计数
                SingletonMgr.Instance.BattleMgr.DecrementExplosionCount();
                throw;
            }
            catch (Exception ex)
            {
                // V60.0 异常时也要减少计数
                SingletonMgr.Instance.BattleMgr.DecrementExplosionCount();
                //Debug.LogException(ex);
            }
        }

        /// <summary>
        /// V59.0 优化爆炸目标获取，分层筛选提升性能
        /// </summary>
        /// <param name="pos">爆炸位置</param>
        /// <param name="exploseRadius">爆炸半径</param>
        /// <returns>被爆炸影响的敌人列表</returns>
        private List<CreatureThing> GetOptimizedExplosionTargets(Vector3 pos, float exploseRadius)
        {
            var allMonsters = SingletonMgr.Instance.BattleMgr.Monsters;
            if (allMonsters == null || allMonsters.Count == 0)
            {
                Debug.Log("V59.0 场景中没有怪物，跳过爆炸检测");
                return new List<CreatureThing>();
            }

            int totalMonsters = allMonsters.Count;
            Debug.Log($"V59.0 爆炸优化检测开始：总怪物数{totalMonsters}，爆炸半径{exploseRadius}");

            // 第一层：快速距离筛选（使用平方距离避免开方运算）
            float radiusSquared = exploseRadius * exploseRadius;
            var candidateMonsters = new List<(MonsterThing monster, float distanceSquared)>();
            
            foreach (var monster in allMonsters)
            {
                if (monster?.Position == null) continue;
                
                // 计算平方距离，避免开方运算
                Vector3 diff = monster.Position - pos;
                float distanceSquared = diff.x * diff.x + diff.y * diff.y;
                
                // 预估最大可能半径（怪物半径通常不超过20），提前筛选
                float maxPossibleRadius = exploseRadius + 20f;
                float maxDistanceSquared = maxPossibleRadius * maxPossibleRadius;
                
                if (distanceSquared <= maxDistanceSquared)
                {
                    candidateMonsters.Add((monster, distanceSquared));
                }
            }
            
            Debug.Log($"V59.0 第一层筛选完成：{candidateMonsters.Count}/{totalMonsters} 个怪物进入候选");

            // 第二层：精确圆形碰撞检测
            var validTargets = new List<CreatureThing>();
            int processedCount = 0;
            
            foreach (var (monster, distanceSquared) in candidateMonsters)
            {
                // 获取怪物半径（缓存机制）
                float monsterRadius = GetCachedMonsterRadius(monster);
                
                // 精确圆形碰撞检测
                float distance = pos.CalcDistance2D_SolidCircleToSolidCircle(exploseRadius, monster.Position, monsterRadius);
                
                if (distance <= float.Epsilon)
                {
                    validTargets.Add(monster);
                }
                
                processedCount++;
                
                // 分批处理：每处理10个怪物检查一次取消令牌（避免长时间阻塞）
                if (processedCount % 10 == 0)
                {
                    // 这里可以添加取消检查或分帧处理
                }
            }
            
            Debug.Log($"V59.0 爆炸优化检测完成：{validTargets.Count} 个怪物受到爆炸影响，处理了{processedCount}个候选目标");
            return validTargets;
        }
        
        /// <summary>
        /// V59.0 获取缓存的怪物半径，避免重复调用GetTotalDouble
        /// </summary>
        private Dictionary<MonsterThing, float> _monsterRadiusCache = new Dictionary<MonsterThing, float>();
        
        private float GetCachedMonsterRadius(MonsterThing monster)
        {
            if (!_monsterRadiusCache.TryGetValue(monster, out float radius))
            {
                radius = (float)monster.GetTotalDouble(PropType.Radius).FirstOrDefault();
                _monsterRadiusCache[monster] = radius;
                
                // 缓存清理：当缓存大小超过100时，清理一半
                if (_monsterRadiusCache.Count > 100)
                {
                    var itemsToRemove = _monsterRadiusCache.Take(_monsterRadiusCache.Count / 2).ToList();
                    foreach (var item in itemsToRemove)
                    {
                        _monsterRadiusCache.Remove(item.Key);
                    }
                    Debug.Log("V59.0 半径缓存已清理，释放内存");
                }
            }
            return radius;
        }
    }
}