using UnityEngine;
using System.Diagnostics;

namespace Test
{
    /// <summary>
    /// V60.0 批量死亡优化测试脚本
    /// 用于验证大量怪物瞬间死亡的性能优化效果
    /// </summary>
    public class V60_BatchDeathOptimizationTest : MonoBehaviour
    {
        void Update()
        {
            // 按空格键运行批量死亡测试
            if (Input.GetKeyDown(KeyCode.Space))
            {
                TestBatchDeathPerformance();
            }
            
            // 按B键测试爆炸控制
            if (Input.GetKeyDown(KeyCode.B))
            {
                TestExplosionControl();
            }
            
            // 按D键测试飘字队列
            if (Input.GetKeyDown(KeyCode.D))
            {
                TestDamageTextQueue();
            }
        }

        /// <summary>
        /// 测试批量死亡处理性能
        /// </summary>
        void TestBatchDeathPerformance()
        {
            var gameManager = SingletonMgr.Instance.GameManager;
            if (gameManager?.Monsters == null)
            {
                UnityEngine.Debug.Log("V60.0 测试失败：GameManager或怪物列表为空");
                return;
            }

            int monsterCount = gameManager.Monsters.Count;
            UnityEngine.Debug.Log($"V60.0 批量死亡测试开始：当前怪物数 {monsterCount}");

            if (monsterCount == 0)
            {
                UnityEngine.Debug.Log("V60.0 场景中没有怪物，无法测试");
                return;
            }

            var stopwatch = Stopwatch.StartNew();
            
            // 模拟所有怪物瞬间死亡
            var monstersToKill = new List<MonsterThing>(gameManager.Monsters);
            foreach (var monster in monstersToKill)
            {
                // 触发死亡事件，进入批量处理队列
                MessageBroker.Default.Publish(new ThingDead(monster));
            }
            
            stopwatch.Stop();
            UnityEngine.Debug.Log($"V60.0 批量死亡事件发布完成：{monsterCount} 个怪物，用时 {stopwatch.ElapsedMilliseconds}ms");
            
            // 显示批量处理状态
            UnityEngine.Debug.Log($"V60.0 批量处理队列状态：{gameManager._batchDeathQueue?.Count ?? 0} 个待处理，处理中：{gameManager._isBatchProcessing}");
        }

        /// <summary>
        /// 测试爆炸控制系统
        /// </summary>
        void TestExplosionControl()
        {
            var gameManager = SingletonMgr.Instance.GameManager;
            if (gameManager == null)
            {
                UnityEngine.Debug.Log("V60.0 测试失败：GameManager为空");
                return;
            }

            UnityEngine.Debug.Log("V60.0 测试爆炸控制系统...");
            
            // 测试爆炸限制
            for (int i = 0; i < 10; i++)
            {
                bool canExplode = gameManager.CanTriggerExplosion();
                UnityEngine.Debug.Log($"V60.0 爆炸测试 {i+1}: 可以触发={canExplode}");
                
                if (canExplode)
                {
                    gameManager.IncrementExplosionCount();
                }
            }
            
            // 重置计数器
            for (int i = 0; i < 10; i++)
            {
                gameManager.DecrementExplosionCount();
            }
            
            UnityEngine.Debug.Log("V60.0 爆炸控制测试完成");
        }

        /// <summary>
        /// 测试伤害飘字队列
        /// </summary>
        void TestDamageTextQueue()
        {
            var gameManager = SingletonMgr.Instance.GameManager;
            if (gameManager == null)
            {
                UnityEngine.Debug.Log("V60.0 测试失败：GameManager为空");
                return;
            }

            UnityEngine.Debug.Log("V60.0 测试伤害飘字队列...");
            
            // 快速生成大量飘字请求
            for (int i = 0; i < 50; i++)
            {
                Vector3 randomPos = new Vector3(
                    UnityEngine.Random.Range(-10f, 10f),
                    UnityEngine.Random.Range(-5f, 5f),
                    0
                );
                int damage = UnityEngine.Random.Range(100, 1000);
                bool isCritical = UnityEngine.Random.value > 0.7f;
                bool isPlayer = UnityEngine.Random.value > 0.5f;
                
                gameManager.QueueDamageText(randomPos, damage, isCritical, isPlayer);
            }
            
            UnityEngine.Debug.Log($"V60.0 飘字队列测试：已添加50个飘字请求到队列");
        }

        /// <summary>
        /// GUI显示测试信息
        /// </summary>
        void OnGUI()
        {
            var gameManager = SingletonMgr.Instance.GameManager;
            if (gameManager == null) return;

            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.Label("=== V60.0 批量死亡优化测试 ===", new GUIStyle(GUI.skin.label) { fontSize = 14, fontStyle = FontStyle.Bold });
            
            GUILayout.Space(10);
            GUILayout.Label($"当前怪物数量: {gameManager.Monsters?.Count ?? 0}");
            GUILayout.Label($"批量处理队列: {gameManager._batchDeathQueue?.Count ?? 0}");
            GUILayout.Label($"正在批量处理: {gameManager._isBatchProcessing}");
            GUILayout.Label($"活跃爆炸数量: {gameManager._activeExplosionCount}/5");
            GUILayout.Label($"飘字队列数量: {gameManager._damageTextQueue?.Count ?? 0}");
            
            GUILayout.Space(10);
            GUILayout.Label("测试操作:", new GUIStyle(GUI.skin.label) { fontStyle = FontStyle.Bold });
            GUILayout.Label("空格键: 批量死亡测试");
            GUILayout.Label("B键: 爆炸控制测试");
            GUILayout.Label("D键: 飘字队列测试");
            
            GUILayout.EndArea();
        }
    }
} 