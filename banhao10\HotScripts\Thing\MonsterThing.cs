﻿// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;

using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using CsvTables;

using DataStructure;

using Props;

using ThingCdExecutors;

using UnityEngine;

using View;

using ViewModel;

using X.PB;

namespace Thing
{
    /// <summary>
    ///     怪物物件(数据)
    /// </summary>
    /// <remarks>
    ///     加载属性的顺序：
    ///     CsvRow_BattleBrushEnemy
    ///     怪物的固有属性
    ///     创建枪及其固有属性
    ///     怪物和枪的附着属性分发到目标上
    ///     怪物提升、枪提升
    /// </remarks>
    public class MonsterThing : CreatureThing
    {
        private BattleBrushEnemy.Item CsvRow_BattleBrushEnemy_m;

        public MonsterThing(object key = default, IBubbleNode parent = null) : base(key, parent)
        {
        }

        /// <summary>
        ///     物件类型
        /// </summary>
        public override ThingType ThingType { get; set; } = ThingType.Monster;

        /// <summary>
        ///     阵营
        /// </summary>
        public override Camp Camp { get; set; } = Camp.Monster;

        /// <summary>
        ///     怪物界面
        /// </summary>
        public MonsterBase Monster => ThingBehaviour as MonsterBase;

        /// <summary>
        ///     怪物是从哪个刷怪配置出生的
        /// </summary>
        public BattleBrushEnemy.Item CsvRow_BattleBrushEnemy
        {
            get => CsvRow_BattleBrushEnemy_m;
            set
            {
                CsvRow_BattleBrushEnemy_m = value;
                AiEventList.Clear();
                try
                {
                    string[] Ids = CsvRow_BattleBrushEnemy_m.BossCG.Split(";", StringSplitOptions.RemoveEmptyEntries);
                    if (Ids is { Length: > 0 })
                    {
                        AiEventList.AddRange(Ids.ToList().ConvertAll(int.Parse).Where(id => id > 0).Distinct()
                            .Select(id => new AiEventState
                            {
                                CsvRow_AiEvent = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<AiEventCsv>()
                                    .Dic[id]
                            }));
                    }
                }
                catch
                {
                    // ignore
                }
            }
        }

        public List<AiEventState> AiEventList { get; } = new();

        /// <summary>
        ///     能否将属性附着于怪物
        /// </summary>
        public override bool CanAttach(CommonProp prop)
        {
            if (prop.AttachedType == AttachedType.Monster)
            {
                // AttachTo是否满足
                if (!prop.AttachTo.Contains(CsvRow_BattleBrushEnemy.Id))
                {
                    return false;
                }
            }

            // 运行到这里，就是可附着了
            return true;
        }

        #region 重新加载固有属性

        /// <inheritdoc />
        public override List<CommonProp> FindInherentPropRows()
        {
            Dictionary<int, List<CommonProp>> dic =
                SingletonMgr.Instance.GlobalMgr.GetMonsterPropIds(CsvRow_BattleBrushEnemy);
            return dic[CsvRow_BattleBrushEnemy.Id];
        }

        #endregion

        /// <inheritdoc />
        public override bool PickProps()
        {
            // 从自己的附着属性中提取
            bool rtn = base.PickProps();

            // 从玩家的附着属性中提取
            List<CommonProp> lst1 = SingletonMgr.Instance.BattleMgr.Actor.AttachedProps.FindPropsCanApplyTo(this);
            lst1.ForEach(p => AddProp(p));

            // 从玩家的枪的附着属性中提取
            List<CommonProp> lst2 = SingletonMgr.Instance.BattleMgr.Actor.Guns
                .SelectMany(g => g.AttachedProps.FindPropsCanApplyTo(this))
                .ToList();
            lst2.ForEach(p => AddProp(p));

            return rtn || lst1.Count > 0;
        }

        /// <inheritdoc />
        public override bool HasEquip(int GoodsId, int starExp = 0)
        {
            return SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[CsvRow_BattleBrushEnemy.SkillID]
                .GoodsID == GoodsId;
        }

        //public override bool CanAddBuff(BuffThing buff)
        //{
        //	return buff.BuffId is not (100 or 101 or 102) // 非0移动方式 不接受 定身、击退、减速
        //		;
        //	//return GetHoistedLong(PropType.MoveMethod).FirstOrDefault() == 0 || // 0移动方式 可以接受任何Buff
        //	//	   buff.BuffId is not (100 or 101 or 102) // 非0移动方式 不接受 定身、击退、减速
        //	//	;
        //}
        //

        /// <inheritdoc />
        public override void ReCreateGuns(List<GunItem> gunsInBag, bool apply = false)
        {
            Guns.Where(p => p.IsHidden).ToList().ForEach(g => g.StopCdExecutor());
            Guns.Clear();

            // 每件出战的武器,创建一个隐藏的枪
            gunsInBag.Where(g => g.GunId > 0).ToList().ForEach(g =>
            {
                (GunThing gun, _) = AddGun(g, apply);
                gun.Camp = Camp;
                gun.IsHidden = true;
            });
        }

        protected override void Hp_Changed(ChangeEventArgs e)
        {
            base.Hp_Changed(e);

            double originalValue = e.OriginalValue is double vo ? vo : 0;
            double newValue = e.NewValue is double vn ? vn : 0;
            double inc = newValue - originalValue;
            //玩家吸血
            if (inc < 0)
            {
                double playerHpAbsorb = SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.HpAbsorb)
                    .FirstOrDefault();
                if (playerHpAbsorb > 0)
                {
                    // var suck = System.Math.Floor(playerHpAbsorb * -inc);
                    double nActorHP = SingletonMgr.Instance.BattleMgr.Actor.Hp.Value + playerHpAbsorb;
                    nActorHP = Math.Clamp(nActorHP, SingletonMgr.Instance.BattleMgr.Actor.Hp.Value,
                        SingletonMgr.Instance.BattleMgr.Actor.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
                    SingletonMgr.Instance.BattleMgr.Actor.Hp.Value = nActorHP;
                }
            }
        }

        /// <inheritdoc />
        public override bool TakeHit(ThingBase attacker, double damage, bool isCritical = false)
        {
            bool rtn = base.TakeHit(attacker, damage, isCritical);

            _ = AiEventList.Where(x =>
                x.CanTakeHitRebirth && x.LastTime_TakeHitRebirth + (x.CsvRow_AiEvent.EventParameter / 1000f) <
                Time.time).Select(x =>
            {
                x.LastTime_TakeHitRebirth = Time.time;
                int mId = x.GetRandomRebirthId();
                if (mId > 0)
                {
                    SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.BornMonster(Position, 3,
                        new List<int> { mId }, new List<int> { 1 });
                }

                return x;
            }).ToList();

            if (rtn)
            {
                // 奖励玩家击杀后Buff
                SingletonMgr.Instance.BattleMgr.Actor.ReceiveBuffByKillEnemy(attacker);
            }

            return rtn;
        }

        /// <inheritdoc />
        public override BulletThing CreateBullet(ThingCdExecutor shooter, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, float angle, int penetrateTimes, int bounceTimes, int separateTimes,
            ICollection<MonsterThing> monsters = null
        )
        {
            BulletThing rtn = base.CreateBullet(shooter, attackBaseDirFollowThing, trackPos, angle, penetrateTimes,
                bounceTimes,
                separateTimes, monsters);

            BulletType bulletType = (BulletType)(int)rtn.GetTotalLong(PropType.BulletType).FirstOrDefault();
            if (bulletType == BulletType.Bullet)
            {
                rtn.AttackBaseDirFollowThing = attackBaseDirFollowThing;
            }
            else if (bulletType == BulletType.MissileTrackPos)
            {
                rtn.TrackPosition = trackPos;
                if (angle > 0 && trackPos.HasValue)
                {
                    // 绕起点旋转
                    rtn.TrackPosition = trackPos.Value.RotateAround(Vector3.forward, angle, Position);
                }
            }

            return rtn;
        }
    }
}