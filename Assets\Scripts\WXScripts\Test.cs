using System.Collections;
using System.Collections.Generic;
using System.IO;
using Cysharp.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.ResourceManagement.ResourceProviders;
using UnityEngine.AddressableAssets;
using UnityEngine.InputSystem;
using UnityEngine.Events;
using DG.Tweening.Plugins.Core.PathCore;

public class WXTest : MonoBehaviour
{
    // Start is called before the first frame update
    void Start()
    {
        Debug.Log(Application.persistentDataPath);
        string path = "Assets/Temp/model/prefab/Bullet/JS_huan_Nv01_skill01_hit.pan";
        Debug.Log(path.Substring(path.LastIndexOf("/")+1,path.LastIndexOf(".")-path.LastIndexOf("/")-1));
        //List<string> keys = new List<string>() { "Assets/Temp/ui/Common/MaterialManager.prefab", "Assets/Temp/ui/Battle/UpgradeSkillView.prefab" };
        //LoadResListAsync<GameObject>(keys,null);
        //StartCoroutine(DownloadLua());
        //LuaPack();
        //DownloadLua2();
        //ResMgr.Instance.DownloadLuaAB();
        //ResMgr.Instance.DownloadpbAB();
        //AsyncOperationHandle<SceneInstance> operation = ResMgr.LoadSceneAsync("Assets/Scenes/GameLogin.unity");
        //Debug.Log(operation.PercentComplete);
    }

    public static void LuaPack()
    {
        string luaDir = Application.dataPath + "/Temp/GameLua";
        string outDir = Application.dataPath.Replace("Assets", "ServerData/lua.bytes");
        Debug.Log(luaDir);
        Debug.Log(outDir);

        AssetBundle bundle = AssetBundle.LoadFromFile(Application.streamingAssetsPath+"/lua.unity3d");
        TextAsset textAsset = bundle.LoadAsset<TextAsset>("Main.lua");
        Debug.Log(textAsset.text);
    }

    private IEnumerator DownloadLua()
    {
        using (UnityWebRequest request = UnityWebRequest.Get("https://cdn.yinuowan.vip/wechatgame/wxTestCDN/lua.unity3d"))
        {
            yield return request.SendWebRequest();
            if (request.isDone)
            {
                Debug.Log(request.downloadHandler.data.Length);
                File.WriteAllBytes(Application.persistentDataPath + "/StreamingAssets/lua.unity3d", request.downloadHandler.data);
            }
        }
    }

    private async UniTaskVoid DownloadLua2(string key)
    {
        var result = await Addressables.DownloadDependenciesAsync(key).Task;
        
        //TextAsset luaText = ResMgr.LoadRes<TextAsset>("Assets/Temp/GameLuaTxt/Main.lua.txt");
        //Debug.Log(luaText.text);
    }

    public static async UniTaskVoid LoadResListAsync<T>(List<string> resPaths, UnityAction<List<T>> onComplete) where T : UnityEngine.Object
    {
        
        await Addressables.LoadAssetsAsync<T>(resPaths, (resList) => 
        {
            Debug.Log(resList.name);
        },Addressables.MergeMode.Union).Task;
        Debug.Log("加载完成");
    }
}
